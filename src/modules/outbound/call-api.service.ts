import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import {
  CallApiRequest,
  CallApiResponse,
  OutboundRecord,
} from './interfaces/outbound.interface';
import * as process from 'node:process';
import { CustomLoggerService } from '../../shared/custom-logger.service';
import { Outbound } from './entities/outbound.entity';

@Injectable()
export class CallApiService {
  private readonly apiUrl = process.env.NCVAV_URL || 'https://capi.ncvav.com/call/call';
  private readonly token = process.env.NCVAV_TOKEN;
  private readonly defaultCallerId = process.env.DEFAULT_CALLER_ID || '902129454744';
  private readonly destinationNumber = process.env.DESTINATION_NUMBER || '902129454744';




  constructor(
    private readonly httpService: HttpService,
    private readonly logger: CustomLoggerService
  ) {}

  /**
   *
   * @param record
   * @param responseurl
   * @param destinationNumber
   */
  async makeCall(
    record: OutboundRecord | Outbound,
    responseurl?: string,
    destinationNumber?: string
  ): Promise<CallApiResponse> {
    try {
      const requestData: CallApiRequest = {
        application: 'OUTBOUND',
        destination: destinationNumber || this.destinationNumber,
        callerid: this.defaultCallerId,
        responseurl: responseurl || '',
        variable: '',
        vmdetect: '0',
        caller: {
          '1': record.number,
        },
      };

      this.logger.log(`Making call to ${record.number} (${record.name} ${record.surname})`);
      this.logger.log('Request data:' + JSON.stringify(requestData));

      const response = await firstValueFrom(
        this.httpService.post(this.apiUrl, requestData, {
          headers: {
            token: this.token,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        })
      );

      this.logger.log('API Response:' + JSON.stringify(response.data));
      // Parse the response based on the API's actual response format
      // You may need to adjust this based on the actual API response structure
      if (response.status === 200) {
        return {
          success: true,
          message: 'Call initiated successfully',
          requestData: requestData,
          responseData: response.data,
          callId: response.data?.callinfo?.[0]?.uniqueid || `call_${Date.now()}`,
        };
      } else {
        return {
          success: false,
          error: `API returned status ${response.status}`,
        } as any;
      }
    } catch (error) {
      this.logger.error(`Error making call to ${record.number}:`, error);

      let errorMessage = 'Unknown error occurred';
      if (error.response) {
        errorMessage = `API Error: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'Network error: No response received from API';
      } else {
        errorMessage = error.message || 'Request setup error';
      }

      return {
        success: false,
        error: errorMessage,
      } as any;
    }
  }

  /**
   *
   */
  async testConnection(): Promise<boolean> {
    try {
      const testData: CallApiRequest = {
        application: 'OUTBOUND',
        destination: this.destinationNumber,
        callerid: this.defaultCallerId,
        responseurl: '',
        variable: '',
        vmdetect: '0',
        caller: {
          '1': '905459691896',
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(this.apiUrl, testData, {
          headers: {
            token: this.token,
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 second timeout for test
        })
      );

      return response.status === 200;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }


}
