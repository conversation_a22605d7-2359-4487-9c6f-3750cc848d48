# Specify Node Version and Image
FROM node:22 AS development

# Specify Working directory inside container
WORKDIR /rastmobile/app

COPY package*.json tsconfig.build.json ./
#COPY . ./

# Install deps inside container
RUN yarn install

EXPOSE 3000

################
## PRODUCTION ##
################
# Build another image named production
FROM node:22 AS production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Set work dir
WORKDIR /rastmobile/app

COPY --from=development /rastmobile/app/node_modules node_modules
COPY . ./

RUN npm run build

EXPOSE 3000

# run app
ENTRYPOINT ["node", "dist/src/main"]

